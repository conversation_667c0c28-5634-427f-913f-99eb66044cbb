services:
  postgres:
    image: postgres:15-alpine
    container_name: financial_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - docker-network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}" ]
      interval: 10s
      timeout: 5s
      retries: 5

  finance_indicator_daemon_rabbitmq:
    image: rabbitmq:3-management
    healthcheck:
      test: [ "CMD", "rabbitmqctl", "status" ]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - docker-network
    volumes:
      - ./rabbit_mq/conf/definitions.json:/etc/rabbitmq/definitions.json:ro
      - ./rabbit_mq/conf/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    ports:
      - "5672:5672"
      - "15672:15672"

  # Spring Boot Financial Indicator Daemon
  financial-indicator-daemon:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: financial_indicator_daemon
    depends_on:
      postgres:
        condition: service_healthy
      finance_indicator_daemon_rabbitmq:
        condition: service_healthy
    networks:
      - docker-network
    ports:
      - "6601:6601"
    environment:
      # Database Configuration
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      # RabbitMQ Configuration
      - RABBITMQ_HOST=finance_indicator_daemon_rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=${RABBITMQ_USER:-guest}
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-guest}
      # Application Configuration
      - CMC_API_KEY=${CMC_API_KEY}
      - CMC_API_THROTTLE_MIN=${CMC_API_THROTTLE_MIN:-300}
      - CMC_API_THROTTLE_MAX=${CMC_API_THROTTLE_MAX:-500}
      - CMC_SYMBOL_OVERRIDES=${CMC_SYMBOL_OVERRIDES:-}
      - INDICATOR_API_HOST=${INDICATOR_API_HOST:-http://localhost:6501}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6601/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Financial Data Pipeline Cron Scheduler
  pipeline-cron:
    build:
      context: .
      dockerfile: cron/Dockerfile
    container_name: financial_pipeline_cron
    depends_on:
      finance_indicator_daemon_rabbitmq:
        condition: service_healthy
    networks:
      - docker-network
    environment:
      # RabbitMQ Configuration
      - RABBITMQ_HOST=${RABBITMQ_HOST}
      - RABBITMQ_PORT=${RABBITMQ_PORT}
    restart: unless-stopped
    profiles:
      - cron

networks:
  docker-network:
    external: true

volumes:
  postgres_data:
