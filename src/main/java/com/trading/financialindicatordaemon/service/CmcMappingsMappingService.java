package com.trading.financialindicatordaemon.service;

import com.trading.financialindicatordaemon.client.CryptocurrencyMapping;
import com.trading.financialindicatordaemon.mapper.CmcMappingsMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static com.trading.financialindicatordaemon.util.CollectionUtils.partition;

@Service
public class CmcMappingsMappingService {

    private final CmcMappingsMapper cmcMappingsMapper;

    public CmcMappingsMappingService(CmcMappingsMapper cmcMappingsMapper) {
        this.cmcMappingsMapper = cmcMappingsMapper;
    }

    public boolean doMappingsExist() {
        return findMapping("BTC").isPresent();
    }

    public Optional<CryptocurrencyMapping> findMapping(String symbol) {
        return cmcMappingsMapper.findBySymbol(symbol);
    }

    @Transactional
    public void insert(List<CryptocurrencyMapping> mappings) {
        partition(mappings, 5000).forEach(cmcMappingsMapper::insert);
    }

}
