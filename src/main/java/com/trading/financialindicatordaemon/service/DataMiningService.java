package com.trading.financialindicatordaemon.service;

import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuotes;
import com.trading.financialindicatordaemon.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static java.time.ZoneOffset.UTC;

@Service
public class DataMiningService {

    private static final Logger logger = LoggerFactory.getLogger(DataMiningService.class);

    private final CoinMarketCapService coinMarketCapService;
    private final CmcMappingsMappingService cmcMappingsMappingService;
    private final UnixTimestampService unixTimestampService;
    private final CmcCandleDataService cmcCandleDataService;

    public DataMiningService(CoinMarketCapService coinMarketCapService, CmcMappingsMappingService cmcMappingsMappingService, UnixTimestampService unixTimestampService, CmcCandleDataService cmcCandleDataService) {
        this.coinMarketCapService = coinMarketCapService;
        this.cmcMappingsMappingService = cmcMappingsMappingService;
        this.unixTimestampService = unixTimestampService;
        this.cmcCandleDataService = cmcCandleDataService;
    }

    public void mineMappings() {
        logger.info("Starting cryptocurrency mappings retrieval");
        if (cmcMappingsMappingService.doMappingsExist()) {
            logger.info("Mappings already exist, skipping download");
            return;
        }

        cmcMappingsMappingService.insert(coinMarketCapService.getMappings());
        logger.info("Completed cryptocurrency mappings retrieval");
    }

    public void mineSymbols(List<String> symbols, Integer currencyId) {
        logger.info("Starting data mining for symbols: {}", symbols);

        if (!cmcMappingsMappingService.doMappingsExist()) {
            logger.error("Mappings not found, cannot mine symbols");
            throw new RuntimeException("Mappings not found");
        }

        String conversionCurrency = switch (currencyId) {
            case AppConfig.BTC_CURRENCY_ID -> "BTC";
            case AppConfig.USD_CURRENCY_ID -> "USD";
            default -> throw new RuntimeException("Unsupported currency ID: " + currencyId);
        };

        symbols.stream()
                .map(cmcMappingsMappingService::findMapping)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .forEach(mapping -> cmcCandleDataService.insert(
                        mapping.symbol(),
                        conversionCurrency,
                        fetchAllHistoricalQuotes(mapping.symbol(), conversionCurrency, mapping.id(), currencyId)
                ));
    }

    private List<CryptoCandleHistoricalQuote> fetchAllHistoricalQuotes(String symbol, String conversionCurrency, Integer mappingId, Integer currencyId) {
        List<CryptoCandleHistoricalQuote> allQuotes = new ArrayList<>();
        long timeEnd = unixTimestampService.getCurrentUnixTimestamp();

        Optional<Long> latestCloseTimestamp =
                cmcCandleDataService.findLatestCloseTimestamp(symbol, conversionCurrency)
                        .map(localDateTime -> localDateTime.toEpochSecond(UTC));

        if (latestCloseTimestamp.isPresent() && timeEnd - latestCloseTimestamp.get() < 86400) {
            logger.info("Latest close timestamp for {} is within 24h, skipping", symbol);
            return allQuotes;
        }

        if (latestCloseTimestamp.isPresent() && timeEnd - latestCloseTimestamp.get() < 15552000) {
            logger.info("Fetching historical quotes for mapping ID: {} from {} to {}", mappingId, latestCloseTimestamp.get(), timeEnd);
            return coinMarketCapService.findQuotes(mappingId, currencyId, String.valueOf(latestCloseTimestamp.get()), String.valueOf(timeEnd)).getQuotes();
        }

        while (true) {
            logger.info("Fetching historical quotes for mapping ID: {} from {} to {}", mappingId, timeEnd - 15552000, timeEnd);

            String timeStart = String.valueOf(timeEnd - 15552000);

            CryptoCandleHistoricalQuotes historicalQuotes = coinMarketCapService
                    .findQuotes(mappingId, currencyId, timeStart, String.valueOf(timeEnd));

            List<CryptoCandleHistoricalQuote> quotes = historicalQuotes.getQuotes();
            if (quotes.isEmpty()) {
                logger.info("No more historical quotes found for mapping ID: {}", mappingId);
                break;
            }

            allQuotes.addAll(quotes);
            timeEnd -= 15552000;
        }

        return allQuotes;
    }

}
