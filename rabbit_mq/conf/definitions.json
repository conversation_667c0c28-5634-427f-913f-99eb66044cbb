{"rabbit_version": "3.8.0", "users": [{"name": "guest", "password": "guest", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "guest", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "parameters": [], "policies": [], "queues": [{"name": "mine_active_symbols", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "mine_usd_data_by_symbols", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "mine_btc_data_by_symbols", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "create_indicator_data", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "create_statistics", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}, {"name": "create_mappings", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}], "exchanges": [], "bindings": []}